<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title><PERSON> - Senior Analyst Programmer Resume</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', 'Arial', sans-serif;
            font-size: 11pt;
            line-height: 1.15;
            margin: 0.5in;
            color: #333;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #2c5aa0;
            padding-bottom: 10pt;
            margin-bottom: 15pt;
        }
        .name {
            font-size: 18pt;
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 5pt;
        }
        .title {
            font-size: 12pt;
            font-weight: bold;
            color: #666;
            margin-bottom: 8pt;
        }
        .contact {
            font-size: 10pt;
            color: #555;
        }
        .section-header {
            font-size: 12pt;
            font-weight: bold;
            color: #2c5aa0;
            border-bottom: 1px solid #2c5aa0;
            margin-top: 15pt;
            margin-bottom: 8pt;
            padding-bottom: 2pt;
        }
        .job-title {
            font-weight: bold;
            font-size: 11pt;
        }
        .company-date {
            font-style: italic;
            color: #666;
            margin-bottom: 5pt;
        }
        .achievement {
            font-weight: bold;
            color: #2c5aa0;
        }
        .skills-grid {
            display: table;
            width: 100%;
        }
        .skill-row {
            display: table-row;
        }
        .skill-label {
            display: table-cell;
            font-weight: bold;
            width: 25%;
            padding: 2pt 10pt 2pt 0;
            vertical-align: top;
        }
        .skill-content {
            display: table-cell;
            padding: 2pt 0;
        }
        ul {
            margin: 5pt 0;
            padding-left: 15pt;
        }
        li {
            margin-bottom: 3pt;
        }
        .project {
            margin-bottom: 10pt;
        }
        .project-title {
            font-weight: bold;
            margin-bottom: 2pt;
        }
        .project-tech {
            font-style: italic;
            color: #666;
            margin-bottom: 3pt;
        }
    </style>
</head>
<body>

<div class="header">
    <div class="name">JOHN SMITH</div>
    <div class="title">Senior Analyst Programmer</div>
    <div class="contact">
        📧 <EMAIL> | 📱 +**************** | 🌐 LinkedIn: linkedin.com/in/johnsmith | 💻 GitHub: github.com/johnsmith<br>
        📍 City, State | Available for Remote/Hybrid Work
    </div>
</div>

<div class="section-header">PROFESSIONAL SUMMARY</div>
<p>Results-driven Senior Analyst Programmer with 10+ years of experience designing, developing, and implementing enterprise-level software solutions. Proven expertise in full-stack development, system analysis, and team leadership. Successfully delivered 50+ projects, improving system efficiency by 40% and reducing operational costs by $2M annually. Skilled in agile methodologies, database optimization, and cross-functional collaboration.</p>

<div class="section-header">TECHNICAL SKILLS</div>
<div class="skills-grid">
    <div class="skill-row">
        <div class="skill-label">Programming Languages:</div>
        <div class="skill-content">Java, Python, C#, JavaScript, SQL, TypeScript, Kotlin</div>
    </div>
    <div class="skill-row">
        <div class="skill-label">Frameworks & Libraries:</div>
        <div class="skill-content">Spring Boot, React, Angular, .NET Core, Django, Node.js, Hibernate</div>
    </div>
    <div class="skill-row">
        <div class="skill-label">Databases:</div>
        <div class="skill-content">Oracle, MySQL, PostgreSQL, MongoDB, Redis, SQL Server</div>
    </div>
    <div class="skill-row">
        <div class="skill-label">Cloud & DevOps:</div>
        <div class="skill-content">AWS (EC2, S3, RDS, Lambda), Azure, Docker, Kubernetes, Jenkins, Git</div>
    </div>
    <div class="skill-row">
        <div class="skill-label">Tools & Technologies:</div>
        <div class="skill-content">IntelliJ IDEA, Visual Studio, Eclipse, JIRA, Confluence, Maven, Gradle</div>
    </div>
    <div class="skill-row">
        <div class="skill-label">Methodologies:</div>
        <div class="skill-content">Agile/Scrum, Test-Driven Development (TDD), CI/CD, Microservices Architecture</div>
    </div>
</div>

<div class="section-header">PROFESSIONAL EXPERIENCE</div>

<div class="job-title">Senior Analyst Programmer</div>
<div class="company-date">TechCorp Solutions | Jan 2020 - Present</div>
<ul>
    <li>Lead development of enterprise CRM system serving 10,000+ users, resulting in 35% improvement in customer response time</li>
    <li>Architect and implement microservices-based solutions using Spring Boot and Docker, reducing system downtime by 60%</li>
    <li>Mentor team of 5 junior developers, improving code quality metrics by 45% and reducing bug reports by 30%</li>
    <li>Collaborate with business analysts to translate requirements into technical specifications for 15+ concurrent projects</li>
    <li>Optimize database queries and implement caching strategies, improving application performance by 50%</li>
</ul>
<div class="achievement">Key Achievement: Spearheaded migration from monolithic to microservices architecture, saving $500K annually in infrastructure costs</div>

<div class="job-title">Analyst Programmer</div>
<div class="company-date">DataSystems Inc | Mar 2017 - Dec 2019</div>
<ul>
    <li>Developed and maintained Java-based financial reporting system processing $100M+ in daily transactions</li>
    <li>Implemented automated testing framework using JUnit and Selenium, reducing manual testing time by 70%</li>
    <li>Designed RESTful APIs consumed by 8 different client applications, ensuring 99.9% uptime</li>
    <li>Collaborated with cross-functional teams to deliver 20+ features within tight deadlines</li>
    <li>Created comprehensive technical documentation and conducted knowledge transfer sessions</li>
</ul>
<div class="achievement">Key Achievement: Built real-time data processing pipeline that reduced report generation time from 4 hours to 15 minutes</div>

<div class="job-title">Junior Analyst Programmer</div>
<div class="company-date">InnovateTech Ltd | Jun 2014 - Feb 2017</div>
<ul>
    <li>Developed web applications using Java, Spring MVC, and Oracle database for healthcare management system</li>
    <li>Participated in full software development lifecycle from requirements gathering to deployment</li>
    <li>Performed system analysis and created technical specifications for 10+ modules</li>
    <li>Conducted unit testing and debugging, maintaining code coverage above 85%</li>
    <li>Provided production support and resolved critical issues within SLA requirements</li>
</ul>
<div class="achievement">Key Achievement: Developed patient management module that improved clinic efficiency by 25%</div>

<div class="section-header">KEY PROJECTS</div>

<div class="project">
    <div class="project-title">Enterprise Resource Planning (ERP) System</div>
    <div class="project-tech">Technologies: Java, Spring Boot, React, PostgreSQL, AWS</div>
    <ul>
        <li>Led development of comprehensive ERP solution for manufacturing company with 500+ employees</li>
        <li>Implemented inventory management, HR, and financial modules with real-time reporting capabilities</li>
        <li>Achieved 99.8% system availability and reduced manual processes by 80%</li>
    </ul>
</div>

<div class="project">
    <div class="project-title">Real-time Analytics Dashboard</div>
    <div class="project-tech">Technologies: Python, Django, React, MongoDB, Redis</div>
    <ul>
        <li>Built interactive dashboard for business intelligence team to monitor KPIs across multiple departments</li>
        <li>Integrated with 5 different data sources and implemented real-time data streaming</li>
        <li>Enabled data-driven decision making, resulting in 20% increase in operational efficiency</li>
    </ul>
</div>

<div class="project">
    <div class="project-title">Mobile Banking Application</div>
    <div class="project-tech">Technologies: Java, Spring Boot, Angular, MySQL, Docker</div>
    <ul>
        <li>Developed secure mobile banking platform serving 50,000+ customers</li>
        <li>Implemented multi-factor authentication and encryption protocols meeting banking regulations</li>
        <li>Achieved 4.8/5 app store rating and processed $10M+ in monthly transactions</li>
    </ul>
</div>

<div class="section-header">EDUCATION & CERTIFICATIONS</div>

<div class="job-title">Bachelor of Science in Computer Science</div>
<div class="company-date">University of Technology | 2014</div>
<p><em>Relevant Coursework: Data Structures, Algorithms, Database Systems, Software Engineering</em></p>

<p><strong>Certifications:</strong></p>
<ul>
    <li>AWS Certified Solutions Architect - Associate (2022)</li>
    <li>Oracle Certified Professional, Java SE 11 Developer (2021)</li>
    <li>Certified Scrum Master (CSM) (2020)</li>
    <li>Microsoft Certified: Azure Developer Associate (2019)</li>
</ul>

<div class="section-header">ADDITIONAL INFORMATION</div>
<p><strong>Languages:</strong> English (Native), Spanish (Conversational)<br>
<strong>Professional Development:</strong> Regular attendee of tech conferences (JavaOne, AWS re:Invent, SpringOne)<br>
<strong>Open Source Contributions:</strong> Active contributor to Spring Framework and Apache Commons projects<br>
<strong>Publications:</strong> "Optimizing Database Performance in Enterprise Applications" - Tech Journal (2022)</p>

<p style="text-align: center; margin-top: 20pt; font-style: italic;">References available upon request</p>

</body>
</html>
